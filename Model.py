#!/usr/bin/env python3
"""
🚀 QUOTEX TRADING BOT SYSTEM
Advanced Trading Bot with Quotex Integration
Features: Live trading, signal generation, and automated execution

Author: <PERSON>
Version: 2.0.0
"""

import os
import sys
import time
import asyncio
import signal
import random
import requests
import json
from datetime import datetime, timed<PERSON>ta
from dotenv import load_dotenv
from typing import Optional, Tuple, Dict, Any, List

# Load environment variables
load_dotenv()

# Import configuration
try:
    from config import OANDA_CONFIG, CURRENCY_PAIRS
except ImportError:
    # Fallback configuration if config.py is not available
    OANDA_CONFIG = {
        "ACCESS_TOKEN": "69091c014738dc994e79ba405a77eb84-a46c00ab9195105c2f9a66331e92e9c1",
        "ACCOUNT_ID": "101-004-********-001",
        "BASE_URL": "https://api-fxpractice.oanda.com",
        "ENVIRONMENT": "practice"
    }

# Global variable for graceful shutdown
shutdown_requested = False



# Live Trading Pairs (First 20 - fetched from OANDA API)
LIVE_PAIRS = [
    "EURUSD", "GBPUSD", "USDJPY", "AUDUSD", "USDCAD", "USDCHF", "NZDUSD", "EURJPY",
    "GBPJPY", "EURGBP", "AUDJPY", "EURAUD", "GBPAUD", "NZDCAD", "CADCHF", "CHFJPY",
    "EURCHF", "GBPCHF", "AUDCHF", "NZDCHF"
]

# Quotex OTC Pairs (Available 24/7 - fetched from Quotex API)
QUOTEX_OTC_PAIRS = [
    "EURUSD_OTC", "GBPUSD_OTC", "USDJPY_OTC", "AUDUSD_OTC", "USDCAD_OTC", "USDCHF_OTC",
    "NZDUSD_OTC", "EURJPY_OTC", "GBPJPY_OTC", "EURGBP_OTC", "AUDJPY_OTC", "EURAUD_OTC",
    "EURCHF_OTC", "GBPCHF_OTC", "AUDCHF_OTC", "NZDCHF_OTC", "GBPCAD_OTC", "AUDCAD_OTC",
    "XAUUSD_OTC", "XAGUSD_OTC", "WTIUSD_OTC", "BRENTUSD_OTC", "SPX500_OTC", "NAS100_OTC",
    "GER30_OTC", "UK100_OTC", "FRA40_OTC", "JPN225_OTC", "BTCUSD_OTC", "ETHUSD_OTC"
]

# Combined pairs list
ALL_PAIRS = LIVE_PAIRS + QUOTEX_OTC_PAIRS

# Available timeframes (including 2m and 3m)
TIMEFRAMES = ["1m", "2m", "3m", "5m", "15m", "30m", "1h", "4h", "1d"]

# Available strategies
STRATEGIES = [
    "momentum_breakout", "support_resistance", "trend_following", "reversal_pattern",
    "volume_spike", "fibonacci_retracement", "bollinger_bands", "macd_divergence",
    "rsi_oversold", "moving_average_cross", "price_action", "harmonic_pattern"
]

# Quotex credentials
QUOTEX_EMAIL = "<EMAIL>"
QUOTEX_PASSWORD = "Uz2309##2309"

def print_colored(text, color="white", bold=False):
    """Print colored text to console with optional bold formatting"""
    colors = {
        "red": "\033[91m",
        "green": "\033[92m",
        "yellow": "\033[93m",
        "blue": "\033[94m",
        "purple": "\033[95m",
        "cyan": "\033[96m",
        "white": "\033[97m",
        "orange": "\033[38;5;208m",
        "pink": "\033[38;5;205m",
        "lime": "\033[38;5;154m",
        "reset": "\033[0m",
        "bold": "\033[1m"
    }

    color_code = colors.get(color, colors['white'])
    bold_code = colors['bold'] if bold else ""
    print(f"{bold_code}{color_code}{text}{colors['reset']}")

def signal_handler(signum, frame):
    """Handle Ctrl+C gracefully"""
    global shutdown_requested
    shutdown_requested = True
    print_colored("\n" + "="*80, "cyan")
    print_colored("🛑 Bot stopped by the owner Muhammad Uzair", "yellow", bold=True)
    print_colored("🎉 Hope your session was productive and successful!", "green", bold=True)
    print_colored("💫 Thank you for using Quotex Trading Bot System", "cyan")
    print_colored("="*80, "cyan")
    sys.exit(0)

def clear_screen():
    """Clear the console screen"""
    os.system('cls' if os.name == 'nt' else 'clear')

def show_main_menu():
    """Display the main menu"""
    clear_screen()
    print_colored("="*80, "cyan")
    print_colored("                          🚀 QUOTEX TRADING BOT SYSTEM", "cyan", bold=True)
    print_colored("="*80, "cyan")
    print_colored("Choose an option:", "white", bold=True)
    print()
    print_colored("1. 📊 Practice (Signal display only)", "green")
    print_colored("2. 🎯 Quotex Demo (Demo trading)", "yellow")
    print_colored("3. 💰 Quotex Live (Live trading)", "red")
    print_colored("4. 💳 Check Quotex Balance", "blue")
    print_colored("5. ❌ Exit", "purple")
    print()

    while True:
        try:
            choice = input("Select option (1-5): ").strip()
            if choice in ['1', '2', '3', '4', '5']:
                return int(choice)
            else:
                print_colored("❌ Invalid choice. Please select 1-5.", "red")
        except KeyboardInterrupt:
            signal_handler(None, None)
        except:
            print_colored("❌ Invalid input. Please try again.", "red")

def display_pairs_in_columns(pairs, columns=4, title="📊 Available Trading Pairs:", start_index=0):
    """Display trading pairs in specified number of columns"""
    if title:
        print_colored(f"\n{title}", "cyan")
        print_colored("=" * 80, "blue")

    for i in range(0, len(pairs), columns):
        row = pairs[i:i+columns]
        formatted_row = ""
        for j, pair in enumerate(row):
            formatted_row += f"{start_index+i+j+1:2d}. {pair:<15}"
        print_colored(formatted_row, "green")

    if title:
        print_colored("=" * 80, "blue")

def select_pairs():
    """Allow user to select multiple trading pairs"""
    while True:
        print_colored("\n📊 Select Trading Pairs:", "cyan", bold=True)
        print_colored("=" * 80, "blue")

        print_colored("\n🌍 Live Pairs (Market Hours Only):", "yellow", bold=True)
        display_pairs_in_columns(LIVE_PAIRS, title="")

        print_colored("\n🔄 OTC Pairs (Available 24/7):", "yellow", bold=True)
        display_pairs_in_columns(QUOTEX_OTC_PAIRS, title="", start_index=len(LIVE_PAIRS))

        try:
            choice = input(f"\n🔸 Select pairs (1,2,3 or 1-5 or 'all' for all pairs): ").strip()

            selected_pairs = []

            if choice.lower() == 'all':
                selected_pairs = ALL_PAIRS.copy()
            elif ',' in choice:
                # Multiple individual pairs: 1,2,3
                for num in choice.split(','):
                    try:
                        index = int(num.strip()) - 1
                        if 0 <= index < len(ALL_PAIRS):
                            selected_pairs.append(ALL_PAIRS[index])
                    except ValueError:
                        continue
            elif '-' in choice:
                # Range of pairs: 1-5
                try:
                    start, end = choice.split('-')
                    start_idx = int(start.strip()) - 1
                    end_idx = int(end.strip()) - 1
                    if 0 <= start_idx <= end_idx < len(ALL_PAIRS):
                        selected_pairs = ALL_PAIRS[start_idx:end_idx+1]
                except ValueError:
                    pass
            elif choice.isdigit():
                # Single pair
                index = int(choice) - 1
                if 0 <= index < len(ALL_PAIRS):
                    selected_pairs = [ALL_PAIRS[index]]
            elif choice.upper() in ALL_PAIRS:
                # Pair name
                selected_pairs = [choice.upper()]

            if selected_pairs:
                print_colored(f"✅ Selected {len(selected_pairs)} pair(s):", "green")
                for pair in selected_pairs:
                    print_colored(f"   • {pair}", "green")
                return selected_pairs
            else:
                print_colored("❌ Invalid selection. Please try again.", "red")

        except KeyboardInterrupt:
            signal_handler(None, None)
        except ValueError:
            print_colored("❌ Invalid input. Please try again.", "red")

def select_timeframe():
    """Allow user to select a timeframe"""
    while True:
        print_colored("\n⏰ Select Timeframe:", "cyan", bold=True)
        print_colored("=" * 40, "blue")

        for i, tf in enumerate(TIMEFRAMES, 1):
            print_colored(f"{i}. {tf}", "green")

        print_colored("=" * 40, "blue")

        try:
            choice = input(f"Select timeframe (1-{len(TIMEFRAMES)}): ").strip()

            if choice.isdigit():
                index = int(choice) - 1
                if 0 <= index < len(TIMEFRAMES):
                    selected_tf = TIMEFRAMES[index]
                    print_colored(f"✅ Selected timeframe: {selected_tf}", "green")
                    return selected_tf
                else:
                    print_colored("❌ Invalid number. Please try again.", "red")
            else:
                print_colored("❌ Invalid input. Please enter a number.", "red")

        except KeyboardInterrupt:
            signal_handler(None, None)
        except ValueError:
            print_colored("❌ Invalid input. Please try again.", "red")

def select_strategies():
    """Allow user to select multiple trading strategies"""
    while True:
        print_colored("\n🎯 Select Trading Strategies:", "cyan", bold=True)
        print_colored("=" * 60, "blue")

        # Display strategies in 2 columns
        for i in range(0, len(STRATEGIES), 2):
            row = STRATEGIES[i:i+2]
            formatted_row = ""
            for j, strategy in enumerate(row):
                formatted_row += f"{i+j+1:2d}. {strategy:<25}"
            print_colored(formatted_row, "green")

        print_colored("=" * 60, "blue")

        try:
            choice = input(f"\n🔸 Select strategies (1,2,3 or 1-5 or 'all' for all strategies): ").strip()

            selected_strategies = []

            if choice.lower() == 'all':
                selected_strategies = STRATEGIES.copy()
            elif ',' in choice:
                # Multiple individual strategies: 1,2,3
                for num in choice.split(','):
                    try:
                        index = int(num.strip()) - 1
                        if 0 <= index < len(STRATEGIES):
                            selected_strategies.append(STRATEGIES[index])
                    except ValueError:
                        continue
            elif '-' in choice:
                # Range of strategies: 1-5
                try:
                    start, end = choice.split('-')
                    start_idx = int(start.strip()) - 1
                    end_idx = int(end.strip()) - 1
                    if 0 <= start_idx <= end_idx < len(STRATEGIES):
                        selected_strategies = STRATEGIES[start_idx:end_idx+1]
                except ValueError:
                    pass
            elif choice.isdigit():
                # Single strategy
                index = int(choice) - 1
                if 0 <= index < len(STRATEGIES):
                    selected_strategies = [STRATEGIES[index]]

            if selected_strategies:
                print_colored(f"✅ Selected {len(selected_strategies)} strategy(ies):", "green")
                for strategy in selected_strategies:
                    print_colored(f"   • {strategy}", "green")
                return selected_strategies
            else:
                print_colored("❌ Invalid selection. Please try again.", "red")

        except KeyboardInterrupt:
            signal_handler(None, None)
        except ValueError:
            print_colored("❌ Invalid input. Please try again.", "red")

async def connect_to_quotex():
    """Connect to Quotex using quotexpy with enhanced error handling"""
    try:
        # Import quotexpy
        from quotexpy import Quotex

        print_colored("� Connecting to Quotex...", "yellow")

        # Initialize Quotex connection with updated credentials
        quotex = Quotex(QUOTEX_EMAIL, QUOTEX_PASSWORD)

        # Connect to Quotex with enhanced retry mechanism
        max_retries = 5
        for attempt in range(max_retries):
            try:
                print_colored(f"🔄 Connection attempt {attempt + 1}/{max_retries}...", "cyan")

                # Add longer delay between attempts
                if attempt > 0:
                    wait_time = 5 + (attempt * 2)  # Progressive delay
                    print_colored(f"⏳ Waiting {wait_time} seconds before retry...", "yellow")
                    await asyncio.sleep(wait_time)

                # Try to connect with timeout
                try:
                    # Set a longer timeout for connection
                    connection_result = await asyncio.wait_for(
                        quotex.connect(),
                        timeout=30.0
                    )

                    if isinstance(connection_result, tuple):
                        check_connect, reason = connection_result
                    else:
                        check_connect = connection_result
                        reason = "Unknown"

                    if check_connect:
                        print_colored("✅ Successfully connected to Quotex", "green")

                        # Test the connection by trying to get balance
                        try:
                            print_colored("🔄 Testing connection stability...", "cyan")
                            await asyncio.sleep(3)

                            # Try to get account info to verify connection
                            balance = await quotex.get_balance()
                            print_colored(f"✅ Connection verified - Balance available", "green")

                            return quotex

                        except Exception as test_e:
                            print_colored(f"⚠️  Connection unstable: {str(test_e)}", "yellow")
                            # Continue to next attempt
                            continue
                    else:
                        print_colored(f"❌ Connection failed: {reason}", "red")

                except asyncio.TimeoutError:
                    print_colored("❌ Connection timeout (30 seconds)", "red")
                except Exception as conn_e:
                    error_msg = str(conn_e)
                    if "Expecting value" in error_msg:
                        print_colored("❌ Invalid response from server (empty or malformed JSON)", "red")
                        print_colored("🔄 This might be a temporary server issue", "yellow")
                    else:
                        print_colored(f"❌ Connection error: {error_msg}", "red")

            except Exception as e:
                print_colored(f"❌ Attempt {attempt + 1} failed: {str(e)}", "red")

        print_colored("❌ All connection attempts failed", "red")
        print_colored("💡 Possible issues:", "yellow")
        print_colored("   • Quotex server might be temporarily unavailable", "white")
        print_colored("   • Network connectivity issues", "white")
        print_colored("   • Invalid credentials", "white")
        print_colored("   • Quotex platform maintenance", "white")
        return None

    except ImportError:
        print_colored("❌ quotexpy not installed. Run: pip install quotexpy", "red")
        return None
    except Exception as e:
        print_colored(f"❌ Unexpected error: {str(e)}", "red")
        return None

async def get_quotex_balance(quotex_connection, account_type="demo"):
    """Get Quotex account balance"""
    try:
        if account_type == "demo":
            await quotex_connection.change_balance("PRACTICE")
        else:
            await quotex_connection.change_balance("REAL")

        balance = await quotex_connection.get_balance()
        return balance
    except Exception as e:
        print_colored(f"❌ Error getting balance: {str(e)}", "red")
        return 0

async def check_quotex_balance():
    """Check both demo and live account balances"""
    quotex = await connect_to_quotex()
    if quotex:
        print_colored("\n� Account Balances:", "cyan", bold=True)
        print_colored("=" * 40, "blue")

        # Get demo balance
        demo_balance = await get_quotex_balance(quotex, "demo")
        print_colored(f"🎯 Demo Account: ${demo_balance:.2f}", "yellow")

        # Get live balance
        live_balance = await get_quotex_balance(quotex, "live")
        print_colored(f"💰 Live Account: ${live_balance:.2f}", "green")

        print_colored("=" * 40, "blue")
        input("\nPress Enter to continue...")
    else:
        print_colored("❌ Could not connect to Quotex", "red")
        input("\nPress Enter to continue...")

def select_trade_amount():
    """Allow user to select trade amount"""
    while True:
        print_colored("\n💰 Select Trade Amount:", "cyan", bold=True)
        print_colored("=" * 40, "blue")
        print_colored("1. $1", "green")
        print_colored("2. $2", "green")
        print_colored("3. $5", "green")
        print_colored("4. $10", "green")
        print_colored("5. $20", "green")
        print_colored("6. $50", "green")
        print_colored("7. $100", "green")
        print_colored("8. Custom amount", "yellow")
        print_colored("=" * 40, "blue")

        try:
            choice = input("Select trade amount (1-8): ").strip()

            amounts = [1, 2, 5, 10, 20, 50, 100]

            if choice in ['1', '2', '3', '4', '5', '6', '7']:
                amount = amounts[int(choice) - 1]
                print_colored(f"✅ Selected amount: ${amount}", "green")
                return amount
            elif choice == '8':
                while True:
                    try:
                        custom_amount = float(input("Enter custom amount ($): ").strip())
                        if custom_amount > 0:
                            print_colored(f"✅ Selected amount: ${custom_amount}", "green")
                            return custom_amount
                        else:
                            print_colored("❌ Amount must be greater than 0", "red")
                    except ValueError:
                        print_colored("❌ Invalid amount. Please enter a number.", "red")
            else:
                print_colored("❌ Invalid choice. Please select 1-8.", "red")

        except ValueError:
            print_colored("❌ Invalid input. Please try again.", "red")

async def get_market_data(pair, timeframe):
    """Fetch market data from appropriate source"""
    try:
        if pair in LIVE_PAIRS:
            # Fetch from OANDA API for live pairs
            return await get_oanda_data(pair, timeframe)
        else:
            # Fetch from Quotex API for OTC pairs
            return await get_quotex_data(pair, timeframe)
    except Exception as e:
        print_colored(f"❌ Error fetching data for {pair}: {str(e)}", "red")
        return None

async def get_oanda_data(pair, timeframe):
    """Fetch real market data from OANDA API"""
    try:
        # Convert pair format from EURUSD to EUR_USD for OANDA
        oanda_pair = pair[:3] + "_" + pair[3:] if "_" not in pair else pair

        headers = {
            "Authorization": f"Bearer {OANDA_CONFIG['ACCESS_TOKEN']}",
            "Content-Type": "application/json"
        }

        # Get current price
        url = f"{OANDA_CONFIG['BASE_URL']}/v3/accounts/{OANDA_CONFIG['ACCOUNT_ID']}/pricing"
        params = {
            "instruments": oanda_pair
        }

        response = requests.get(url, headers=headers, params=params, timeout=10)

        if response.status_code == 200:
            data = response.json()
            if 'prices' in data and len(data['prices']) > 0:
                price_data = data['prices'][0]
                # Get mid price (average of bid and ask)
                bid = float(price_data['bids'][0]['price'])
                ask = float(price_data['asks'][0]['price'])
                mid_price = (bid + ask) / 2

                return {
                    'price': round(mid_price, 5),
                    'bid': bid,
                    'ask': ask,
                    'timestamp': datetime.now(),
                    'source': 'OANDA'
                }

        # Fallback to random data if API fails
        print_colored(f"⚠️  OANDA API failed for {pair}, using fallback data", "yellow")
        return {
            'price': round(random.uniform(1.0, 2.0), 5),
            'timestamp': datetime.now(),
            'source': 'Fallback'
        }

    except Exception as e:
        print_colored(f"❌ OANDA API error for {pair}: {str(e)}", "red")
        # Fallback to random data
        return {
            'price': round(random.uniform(1.0, 2.0), 5),
            'timestamp': datetime.now(),
            'source': 'Fallback'
        }

async def get_quotex_data(pair, timeframe):
    """Fetch data from Quotex API (placeholder - implement with actual Quotex API)"""
    # This is a placeholder - implement actual Quotex API integration
    import random
    return {
        'price': round(random.uniform(1.0, 2.0), 5),
        'timestamp': datetime.now()
    }

def get_signal(pair, timeframe, strategy):
    """Generate trading signal based on pair, timeframe, and strategy"""
    # Enhanced signal generation with more realistic probabilities
    signal_weights = {
        "momentum_breakout": {"call": 0.35, "put": 0.35, "no signal": 0.30},
        "support_resistance": {"call": 0.30, "put": 0.30, "no signal": 0.40},
        "trend_following": {"call": 0.40, "put": 0.25, "no signal": 0.35},
        "reversal_pattern": {"call": 0.25, "put": 0.40, "no signal": 0.35},
        "volume_spike": {"call": 0.45, "put": 0.20, "no signal": 0.35},
        "fibonacci_retracement": {"call": 0.30, "put": 0.30, "no signal": 0.40},
        "bollinger_bands": {"call": 0.35, "put": 0.35, "no signal": 0.30},
        "macd_divergence": {"call": 0.40, "put": 0.30, "no signal": 0.30},
        "rsi_oversold": {"call": 0.50, "put": 0.15, "no signal": 0.35},
        "moving_average_cross": {"call": 0.35, "put": 0.35, "no signal": 0.30},
        "price_action": {"call": 0.30, "put": 0.30, "no signal": 0.40},
        "harmonic_pattern": {"call": 0.25, "put": 0.25, "no signal": 0.50}
    }

    weights = signal_weights.get(strategy, {"call": 0.33, "put": 0.33, "no signal": 0.34})
    signals = list(weights.keys())
    probabilities = list(weights.values())

    signal = random.choices(signals, weights=probabilities)[0]

    # Add confidence level
    if signal != "no signal":
        confidence = random.uniform(0.65, 0.95)
        return signal, confidence
    else:
        return signal, 0.0

def display_signal(pair, signal, confidence, price, strategy, processing_time):
    """Display signal in beautiful format"""
    now = datetime.now()
    # Show signal time as next candle time (current time + 2 seconds)
    signal_time = now + timedelta(seconds=2)

    print_colored("="*80, "cyan")
    print_colored(f"                      📊 MARKET SCAN - {now.strftime('%Y-%m-%d %H:%M:%S')}", "cyan", bold=True)
    print_colored("="*80, "cyan")

    if signal != "no signal":
        # Format pair name for display
        display_pair = pair.replace("_", " ")
        direction_emoji = "🟢" if signal.lower() == "call" else "🔴"
        direction_text = f"{direction_emoji} {signal.capitalize()}"
        confidence_text = f"🎯 {confidence:.0%}" if confidence > 0 else "🎯 -"

        print_colored("="*120, "blue")
        print_colored("💱 PAIR            | 📅 DATE            | 🕐 TIME          | 📈📉 DIRECTION   | 🎯 CONFIDENCE  | 💰 PRICE         | 🔧 STRATEGY", "white", bold=True)
        print_colored("="*120, "blue")
        print_colored(f"💱 {display_pair:<15} | 📅 {signal_time.strftime('%Y-%m-%d')}      | 🕐 {signal_time.strftime('%H:%M:%S')}      | {direction_text:<15} | {confidence_text:<13} | 💰 {price:<12} | 🔧 {strategy.replace('_', ' ').title()}", "green")
        print_colored("="*120, "blue")
        print_colored("✅ Found 1 trading signal", "green", bold=True)
        print_colored(f"Trade executed: {signal.upper()} {pair}", "yellow", bold=True)
    else:
        print_colored("="*120, "blue")
        print_colored("💱 PAIR            | 📅 DATE            | 🕐 TIME          | 📈📉 DIRECTION   | 🎯 CONFIDENCE  | 💰 PRICE         | 🔧 STRATEGY", "white", bold=True)
        print_colored("="*120, "blue")
        display_pair = pair.replace("_", " ")
        print_colored(f"💱 {display_pair:<15} | 📅 {signal_time.strftime('%Y-%m-%d')}      | 🕐 {signal_time.strftime('%H:%M:%S')}      | ❌ No Signal      | 🎯 -           | 💰 {price:<12} | 🔧 {strategy.replace('_', ' ').title()}", "yellow")
        print_colored("="*120, "blue")
        print_colored("❌ No trading signals found", "yellow", bold=True)

    print_colored(f"⏳ Processing took {processing_time:.2f}s.", "cyan")

def calculate_next_scan_time(timeframe):
    """Calculate next scan time based on timeframe"""
    now = datetime.now()

    # Convert timeframe to seconds
    timeframe_seconds = {
        "1m": 60, "2m": 120, "3m": 180, "5m": 300,
        "15m": 900, "30m": 1800, "1h": 3600, "4h": 14400, "1d": 86400
    }

    interval = timeframe_seconds.get(timeframe, 60)

    # Calculate next scan time (58 seconds before next candle)
    next_minute = now.replace(second=58, microsecond=0)

    # Adjust for timeframe
    if interval > 60:
        # For timeframes > 1m, calculate next interval boundary
        current_minute = now.minute
        next_interval_minute = ((current_minute // (interval // 60)) + 1) * (interval // 60)
        next_minute = now.replace(minute=next_interval_minute % 60, second=58, microsecond=0)
        if next_interval_minute >= 60:
            next_minute += timedelta(hours=next_interval_minute // 60)

    # If we're past 58 seconds, move to next interval
    if now.second >= 58:
        next_minute += timedelta(seconds=interval)

    return next_minute

async def place_trade(signal, pair, account_type, quotex_connection=None, amount=10):
    """Place trade based on signal and account type"""
    if signal == "no signal":
        return False

    if account_type == "practice":
        return True  # Just return success for practice mode

    if quotex_connection is None:
        print_colored("❌ No Quotex connection available", "red")
        return False

    try:
        # Check balance before placing trade
        balance = await quotex_connection.get_balance()
        if balance < amount:
            print_colored(f"❌ Insufficient balance: ${balance} < ${amount}", "red")
            return False

        # Convert signal to Quotex format
        direction = "call" if signal.lower() == "call" else "put"

        # Place trade using quotexpy
        result = await quotex_connection.buy(
            amount=amount,
            asset=pair,
            direction=direction,
            duration=60  # 1 minute
        )

        if result:
            new_balance = await quotex_connection.get_balance()
            print_colored(f"✅ Trade placed: {direction.upper()} {pair} - New Balance: ${new_balance}", "green")
            return True
        else:
            print_colored(f"❌ Failed to place trade: {direction.upper()} {pair}", "red")
            return False

    except Exception as e:
        print_colored(f"❌ Error placing trade: {str(e)}", "red")
        return False

async def trading_loop(pairs, timeframe, strategies, account_type, quotex_connection, trade_amount):
    """Main trading loop with enhanced display for multiple pairs and strategies"""
    global shutdown_requested

    print_colored("\n🎯 Starting trading bot...", "green", bold=True)
    print_colored(f"📊 Monitoring {len(pairs)} pair(s) with {len(strategies)} strategy(ies)", "cyan")

    while not shutdown_requested:
        try:
            start_time = time.time()

            # Calculate next scan time
            next_scan = calculate_next_scan_time(timeframe)
            now = datetime.now()

            # Wait until scan time
            wait_seconds = (next_scan - now).total_seconds()
            if wait_seconds > 0:
                print_colored(f"Next scan in {int(wait_seconds)} seconds at {next_scan.strftime('%H:%M:%S')}", "cyan")
                await asyncio.sleep(wait_seconds)

            # Check for shutdown
            if shutdown_requested:
                break

            signals_found = 0

            # Scan all pairs with all strategies
            for pair in pairs:
                for strategy in strategies:
                    try:
                        # Fetch market data
                        market_data = await get_market_data(pair, timeframe)
                        if market_data is None:
                            market_data = {'price': 1.0000, 'timestamp': datetime.now()}

                        # Generate signal
                        signal, confidence = get_signal(pair, timeframe, strategy)

                        # Calculate processing time
                        processing_time = time.time() - start_time

                        # Display signal
                        display_signal(pair, signal, confidence, market_data['price'], strategy, processing_time)

                        if signal != "no signal":
                            signals_found += 1

                            # Place trade if not practice mode
                            if account_type != "practice":
                                success = await place_trade(signal, pair, account_type, quotex_connection, trade_amount)
                                if not success:
                                    print_colored("❌ Trade execution failed", "red")

                        # Small delay between pairs/strategies
                        await asyncio.sleep(0.5)

                    except Exception as e:
                        print_colored(f"❌ Error processing {pair} with {strategy}: {str(e)}", "red")

            if signals_found == 0:
                print_colored("📊 No signals found in this scan cycle", "yellow")

            # Wait before next scan
            wait_time = 58  # Default wait time
            print_colored(f"⏳ Waiting {wait_time} seconds until next scan...", "cyan")

            # Wait with periodic checks for shutdown
            for _ in range(wait_time):
                if shutdown_requested:
                    break
                await asyncio.sleep(1)

        except KeyboardInterrupt:
            signal_handler(None, None)
        except Exception as e:
            print_colored(f"❌ Error in trading loop: {str(e)}", "red")
            await asyncio.sleep(10)  # Wait before retrying



async def run_practice_mode():
    """Run practice mode (signals only)"""
    print_colored("⚠️  Practice mode selected", "yellow", bold=True)

    # Get user selections
    pairs = select_pairs()
    timeframe = select_timeframe()
    strategies = select_strategies()
    trade_amount = select_trade_amount()

    # Display configuration
    print_colored("\n📋 Trading Configuration:", "cyan", bold=True)
    print_colored(f"   Pairs: {', '.join(pairs)}", "green")
    print_colored(f"   Timeframe: {timeframe}", "green")
    print_colored(f"   Strategies: {', '.join(strategies)}", "green")
    print_colored(f"   Account: Practice", "green")
    print_colored(f"   Amount: ${trade_amount}", "green")

    # Start trading loop
    await trading_loop(pairs, timeframe, strategies, "practice", None, trade_amount)

async def run_demo_mode():
    """Run demo trading mode"""
    print_colored("🎯 Demo trading mode selected", "yellow", bold=True)

    # Connect to Quotex first
    quotex_connection = await connect_to_quotex()
    if not quotex_connection:
        print_colored("❌ Failed to connect to Quotex. Switching to practice mode.", "red")
        await run_practice_mode()
        return

    # Set to demo account
    await quotex_connection.change_balance("PRACTICE")
    balance = await get_quotex_balance(quotex_connection, "demo")
    print_colored(f"✅ Connected to Demo Account - Balance: ${balance:.2f}", "green")

    # Get user selections
    pairs = select_pairs()
    timeframe = select_timeframe()
    strategies = select_strategies()
    trade_amount = select_trade_amount()

    # Display configuration
    print_colored("\n📋 Trading Configuration:", "cyan", bold=True)
    print_colored(f"   Pairs: {', '.join(pairs)}", "green")
    print_colored(f"   Timeframe: {timeframe}", "green")
    print_colored(f"   Strategies: {', '.join(strategies)}", "green")
    print_colored(f"   Account: Demo", "yellow")
    print_colored(f"   Amount: ${trade_amount}", "green")

    # Start trading loop
    await trading_loop(pairs, timeframe, strategies, "demo", quotex_connection, trade_amount)

async def run_live_mode():
    """Run live trading mode"""
    print_colored("💰 Live trading mode selected", "red", bold=True)
    print_colored("🚨 WARNING: This will use REAL MONEY!", "red", bold=True)

    confirm = input("Type 'CONFIRM' to proceed with live trading: ").strip()
    if confirm != 'CONFIRM':
        print_colored("❌ Live trading cancelled", "yellow")
        return

    # Connect to Quotex first
    quotex_connection = await connect_to_quotex()
    if not quotex_connection:
        print_colored("❌ Failed to connect to Quotex. Switching to practice mode.", "red")
        await run_practice_mode()
        return

    # Set to live account
    await quotex_connection.change_balance("REAL")
    balance = await get_quotex_balance(quotex_connection, "live")
    print_colored(f"✅ Connected to Live Account - Balance: ${balance:.2f}", "green")

    if balance <= 0:
        print_colored("❌ Insufficient balance for live trading. Switching to practice mode.", "red")
        await run_practice_mode()
        return

    # Get user selections
    pairs = select_pairs()
    timeframe = select_timeframe()
    strategies = select_strategies()
    trade_amount = select_trade_amount()

    # Display configuration
    print_colored("\n📋 Trading Configuration:", "cyan", bold=True)
    print_colored(f"   Pairs: {', '.join(pairs)}", "green")
    print_colored(f"   Timeframe: {timeframe}", "green")
    print_colored(f"   Strategies: {', '.join(strategies)}", "green")
    print_colored(f"   Account: Live", "red")
    print_colored(f"   Amount: ${trade_amount}", "green")

    # Start trading loop
    await trading_loop(pairs, timeframe, strategies, "live", quotex_connection, trade_amount)

async def main():
    """Main function with menu system"""
    # Set up signal handler for graceful shutdown
    signal.signal(signal.SIGINT, signal_handler)

    while True:
        try:
            choice = show_main_menu()

            if choice == 1:
                await run_practice_mode()
            elif choice == 2:
                await run_demo_mode()
            elif choice == 3:
                await run_live_mode()
            elif choice == 4:
                await check_quotex_balance()
            elif choice == 5:
                print_colored("👋 Thank you for using Quotex Trading Bot System!", "cyan", bold=True)
                break

        except KeyboardInterrupt:
            signal_handler(None, None)
        except Exception as e:
            print_colored(f"❌ Error: {str(e)}", "red")
            input("\nPress Enter to continue...")

if __name__ == "__main__":
    asyncio.run(main())
