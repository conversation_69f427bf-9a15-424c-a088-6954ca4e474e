[ INFO/2025-07-11 02:35:52,262] quotexpy.api: authenticating user
[ INFO/2025-07-11 02:35:57,484] undetected_chromedriver.patcher: patching driver executable C:\Users\<USER>\appdata\roaming\undetected_chromedriver\undetected_chromedriver.exe
[ INFO/2025-07-11 02:49:29,220] quotexpy.api: authenticating user
[ INFO/2025-07-11 02:49:34,002] undetected_chromedriver.patcher: patching driver executable C:\Users\<USER>\appdata\roaming\undetected_chromedriver\undetected_chromedriver.exe
[ INFO/2025-07-11 02:55:11,055] quotexpy.api: authenticating user
[ INFO/2025-07-11 02:55:15,282] undetected_chromedriver.patcher: patching driver executable C:\Users\<USER>\appdata\roaming\undetected_chromedriver\undetected_chromedriver.exe
[ INFO/2025-07-11 03:03:20,995] quotexpy.api: authenticating user
[ INFO/2025-07-11 03:03:25,735] undetected_chromedriver.patcher: patching driver executable C:\Users\<USER>\appdata\roaming\undetected_chromedriver\undetected_chromedriver.exe
[ INFO/2025-07-11 03:19:53,478] quotexpy.api: authenticating user
[ INFO/2025-07-11 03:19:57,403] undetected_chromedriver.patcher: patching driver executable C:\Users\<USER>\appdata\roaming\undetected_chromedriver\undetected_chromedriver.exe
[ INFO/2025-07-11 03:20:16,261] quotexpy.api: authenticating user
[ INFO/2025-07-11 03:20:36,961] quotexpy.api: authenticating user
[ INFO/2025-07-11 03:35:52,173] quotexpy.api: authenticating user
[ INFO/2025-07-11 03:35:56,204] undetected_chromedriver.patcher: patching driver executable C:\Users\<USER>\appdata\roaming\undetected_chromedriver\undetected_chromedriver.exe
[ INFO/2025-07-11 03:36:16,061] quotexpy.api: authenticating user
[ INFO/2025-07-11 03:36:34,277] quotexpy.api: authenticating user
[ INFO/2025-07-11 03:46:41,739] quotexpy.api: authenticating user
[ INFO/2025-07-11 03:49:30,895] quotexpy.api: authenticating user
[ INFO/2025-07-11 03:52:16,995] quotexpy.api: authenticating user
[ INFO/2025-07-11 04:04:46,505] quotexpy.api: authenticating user
[ INFO/2025-07-11 04:04:51,741] undetected_chromedriver.patcher: patching driver executable C:\Users\<USER>\appdata\roaming\undetected_chromedriver\undetected_chromedriver.exe
[ INFO/2025-07-11 04:05:15,994] quotexpy.api: authenticating user
[ INFO/2025-07-11 04:05:41,314] quotexpy.api: authenticating user
[ INFO/2025-07-11 04:06:07,694] quotexpy.api: authenticating user
[ INFO/2025-07-11 04:06:35,218] quotexpy.api: authenticating user
[ INFO/2025-07-11 12:05:35,182] quotexpy.api: authenticating user
[ INFO/2025-07-11 12:05:49,738] undetected_chromedriver.patcher: patching driver executable C:\Users\<USER>\appdata\roaming\undetected_chromedriver\undetected_chromedriver.exe
[ERROR/2025-07-11 12:05:58,217] quotexpy.api: Connection error: Message: invalid argument: cannot parse capability: goog:chromeOptions
from invalid argument: unrecognized chrome option: excludeSwitches
Stacktrace:
	GetHandleVerifier [0x0xcf44a3+62419]
	GetHandleVerifier [0x0xcf44e4+62484]
	(No symbol) [0x0xb32133]
	(No symbol) [0x0xb59723]
	(No symbol) [0x0xb5aeb0]
	(No symbol) [0x0xb55fea]
	(No symbol) [0x0xba9832]
	(No symbol) [0x0xba931c]
	(No symbol) [0x0xbaaa20]
	(No symbol) [0x0xbaa82a]
	(No symbol) [0x0xb9f266]
	(No symbol) [0x0xb6e852]
	(No symbol) [0x0xb6f6f4]
	GetHandleVerifier [0x0xf64793+2619075]
	GetHandleVerifier [0x0xf5fbaa+2599642]
	GetHandleVerifier [0x0xd1b04a+221050]
	GetHandleVerifier [0x0xd0b2c8+156152]
	GetHandleVerifier [0x0xd11c7d+183213]
	GetHandleVerifier [0x0xcfc388+94904]
	GetHandleVerifier [0x0xcfc512+95298]
	GetHandleVerifier [0x0xce766a+9626]
	BaseThreadInitThunk [0x0x7665fcc9+25]
	RtlGetAppContainerNamedObjectPath [0x0x770282ae+286]
	RtlGetAppContainerNamedObjectPath [0x0x7702827e+238]

[ INFO/2025-07-11 12:06:44,976] quotexpy.api: authenticating user
[ INFO/2025-07-11 12:06:59,367] undetected_chromedriver.patcher: patching driver executable C:\Users\<USER>\appdata\roaming\undetected_chromedriver\undetected_chromedriver.exe
[ERROR/2025-07-11 12:07:51,729] quotexpy.api: Connection error: Expecting value: line 1 column 1 (char 0)
